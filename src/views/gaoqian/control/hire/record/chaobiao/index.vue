<template>
  <div>
    <Card :title="'抄表终端数据'" :size="'small'" style="margin-bottom: 10px">
      <template #extra>
        <Button type="link" @click="handleAdd"> 新增抄表终端 </Button>
      </template>
      <Empty v-if="meterList.length === 0" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
      <Card.Grid style="width: 25%; padding: 1px" :hoverable="false" v-else v-for="item in meterList" :key="item.id">
        <Card :size="'small'" :bordered="false" style="height: 100%">
          <template #title>
            <div class="flex items-center">
              <img v-if="item.type == '1'" src="./水.svg" style="width: 16px; height: 16px; margin-right: 5px" />
              <img v-if="item.type == '4'" src="./电.svg" style="width: 16px; height: 16px; margin-right: 5px" />
              {{ item.name }}
              <!-- <Tag :color="item.activeFlag ? 'success' : 'info'">{{ item.activeFlag ? '启用' : '关闭' }}</Tag> -->
              <Switch
                style="margin-left: 5px"
                :size="'small'"
                :checked-children="'启用'"
                :un-checked-children="'关闭'"
                :checked="item.activeFlag"
                @click="() => handleChange(item)"
              />
            </div>
          </template>
          <template #extra>
            <Button type="link" @click="() => handleEdit(item)" :size="'small'"> 修改 </Button>
            <Button
              type="link"
              @click="() => handleDelete(item)"
              :size="'small'"
              danger
              v-if="hasPermission('system:v2MeterManage:delete')"
            >
              删除
            </Button>
          </template>
          <div style="padding: 10px">
            <div>最新读数：{{ item.liveValue }}</div>
            <div>抄表时间：{{ item.liveTime }}</div>
          </div>
        </Card>
      </Card.Grid>
    </Card>
    <BasicTable @register="registerTable" :searchInfo="searchInfo">
      <template #toolbar>
        <Button @click="() => handleCreate(3)" v-if="hasPermission('system:v2MeterManage:clear')"> 修改读数 </Button>
        <Button @click="() => handleCreate(2)" v-if="hasPermission('system:v2MeterManage:clear')"> 读数清零 </Button>
        <Button @click="() => handleCreate(1)" type="primary"> 新增抄表记录 </Button>
      </template>
      <template #bodyCell="{ column, record }"> </template>
    </BasicTable>
    <ChaobiaoModal @register="registerModal" @success="init" />
  </div>
</template>

<script setup lang="ts">
  import { BasicTable, FormSchema, TableAction, useTable } from '@/components/Table';
  import { columns } from './chaobiao.data';
  import { computed, onMounted, ref } from 'vue';
  import { Card, Empty, Switch, Tag } from 'ant-design-vue';
  import {
    closeKongzhi,
    getKongzhiList,
    addKongzhi,
    getChaobiaoList,
    addChaobiao,
    clearChaobiao,
    changeChaobiao,
    getWaterMeterTerminalList,
    updateTerminalStatus,
    deleteTerminal,
  } from '@/api/gaoqian/manage';
  import { useMessage } from '@/hooks/web/useMessage';
  import { createNewPrompt } from '@/components/NewPrompt';
  import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
  import dayjs from 'dayjs';
  import { Button } from 'ant-design-vue';
  import ChaobiaoModal from './chaobiaoModal.vue';
  import { useModal } from '@/components/Modal';
  import { usePermission } from '@/hooks/web/usePermission';
  const [registerModal, { openModal }] = useModal();
  const { hasPermission } = usePermission();
  const props = defineProps({
    houseInfo: {
      type: Object as PropType<Recordable>,
      default: {},
    },
  });
  function handleSuccess() {
    reload();
  }
  const getParamsByType = (type) => {
    if (type === 1) {
      return {
        title: '新增抄表记录',
        api: addChaobiao,
      };
    } else if (type === 2) {
      return {
        title: '终端读数清零',
        api: clearChaobiao,
      };
    }
    return {
      title: '修改抄表记录',
      api: changeChaobiao,
    };
  };
  function handleCreate(type) {
    const { title, api } = getParamsByType(type);
    const configs = meterList.value.map((item) => ({
      label: item.name,
      value: item.id,
    }));
    const schemas: FormSchema[] = [
      {
        component: 'Select',
        label: '终端类型',
        field: 'meterTerminalId',
        componentProps: {
          options: configs,
        },
      },
      {
        component: 'InputNumber',
        label: type !== 2 ? '抄表读数' : '清零前读数',
        field: 'realValue',
        componentProps: {
          min: 0,
        },
        rules: [{ required: true, message: `请输入${type !== 2 ? '抄表读数' : '清零前读数'}` }],
      },
    ];
    createNewPrompt({
      title,
      labelWidth: 100,
      schemas,
      style: {
        padding: '20px',
      },
      onOK: async (record) => {
        if (type === 3) {
          createConfirm({
            iconType: 'warning',
            title: '提示',
            content: '修改读数会清空待缴费读数，修改完清重新录入抄表数据,是否确认继续操作？',
            onOk: async () => {
              await api(record);
              createMessage.success('操作成功');
              reload();
              init();
            },
          });
        } else {
          await api(record);
          createMessage.success('操作成功');
          reload();
          init();
        }
      },
    });
  }
  function handleEdit(record: Recordable) {
    openModal(true, {
      isUpdate: true,
      houseInfo: props.houseInfo,
      record,
    });
  }
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
      houseInfo: props.houseInfo,
    });
  }
  function handleDelete(record: Recordable) {
    createConfirm({
      iconType: 'warning',
      title: '确认删除',
      content: `确定要删除终端"${record.name}"吗？删除后无法恢复。`,
      onOk: async () => {
        try {
          await deleteTerminal({ id: record.id });
          createMessage.success('删除成功');
          init(); // 重新加载终端列表
        } catch (error) {
          createMessage.error('删除失败');
        }
      },
    });
  }
  async function handleChange(record: Recordable) {
    if (record.liveValue && record.activeFlag) {
      createNewPrompt({
        title: '停用终端',
        labelWidth: 100,
        schemas: [
          {
            component: 'InputNumber',
            label: '最新读数',
            field: 'liveValue',
            componentProps: {
              min: 0,
            },
            rules: [{ required: true, message: '请输入最新读数' }],
          },
        ],
        style: {
          padding: '20px',
        },
        onOK: async (data) => {
          await updateTerminalStatus({
            id: record.id,
            liveValue: data.liveValue,
            activeFlag: !record.activeFlag,
          });
          createMessage.success('操作成功');
          init();
        },
      });
    } else {
      await updateTerminalStatus({
        id: record.id,
        activeFlag: !record.activeFlag,
      });
      createMessage.success('操作成功');
      init();
    }
  }
  const searchInfo = computed(() => {
    return {
      params: {
        comm_Join: 'terminal',
        // not_status: ['待签约', '结束'],
      },
      houseId: props.houseInfo?.id || -999,
    };
  });
  const { createConfirm, createMessage } = useMessage();
  const [registerTable, { reload }] = useTable({
    title: '抄表记录',
    columns,
    api: getChaobiaoList,
    inset: true,
    useSearchForm: false,
    showTableSetting: false,
    bordered: true,
    showIndexColumn: true,
    rowKey: `id`,
  });
  const meterList = ref<Recordable>([]);
  async function init() {
    meterList.value = await getWaterMeterTerminalList({
      houseId: props.houseInfo?.id,
    });

    console.log(meterList.value);
  }
  onMounted(() => {
    init();
  });
</script>

<style scoped></style>
