import { defHttp } from '@/utils/http/axios';
import { exportExcel, formHeader } from '../baseApi';
//水抄记录
enum Api {
  getWaterMeterRecordList = '/system/waterMeterRecord/list',
  exportTemplate = '/system/v2MeterManage/exportTemplate/{id}/{type}',
  importData = '/system/v2MeterManage/importData/{id}/{type}',
  getWaterConfig = '/system/communityFeeConfig/query/water/{id}',

  //查询小区列表
  getCourtyardList = '/system/courtyardInfo/list',
  //设置小区权限
  setCourtyardPermission = '/system/user/userCourtyard',
  //物业缴费第一步
  propertyPayStep1 = '/payment/paymentSlips/property/init/{leaseId}/{countMonth}',
  //物业缴费第二步
  propertyPayStep2 = '/payment/paymentSlips/property/commit',

  //获取暖气记录
  getHeatingRecordList = '/nuanqi/nuanqiLog/list/{id}',
  //生成暖气费
  generateHeatingFee = '/nuanqi/property/nuanqi/{cid}/{year}',
  //获取暖气树
  getHeatingTree = '/nuanqi/getParamTreeList/{id}',
  //移除一条暖气记录
  removeHeatingRecord = '/payment/paymentSlipsDetails/del/{id}',
  //生成暖气费2
  generateHeatingFee2 = '/nuanqi/property/nuanqi/single/{id}/{houseId}',
  //清除暖气费
  clearHeatingFee = '/nuanqi/property/nuanqi/remove/{id}',

  getKongzhiList = '/system/vacancyRecord/list',
  closeKongzhi = '/system/vacancyRecord/edit',
  addKongzhi = '/system/vacancyRecord',
  getCheweiList = '/parking/list/{leaseId}',
  addChewei = '/parking/add',
  closeChewei = '/parking/close',
  deleteChewei = '/parking/remove/{id}',
  createCheweiPayInfo = '/payment/paymentSlips/property/initparking/v2/{leaseId}',

  getChaobiaoList = '/system/v2MeterManage/record/pageList',
  changeChaobiao = '/system/v2MeterManage/terminal/total/edit',
  clearChaobiao = '/system/v2MeterManage/terminal/total/cleared',
  addChaobiao = '/system/v2MeterManage/terminal/total/add',

  tingnuanList = '/system/heatingRecord/list',
  tingnuanCloseList = '/system/heatingClose/list',

  getLastChaobiaoData = '/system/settlementReturn/settlementReturn/water/{leaseId}',
  //获取合约未完成欠费项信息
  getUnfinishedArrears = '/payment/paymentSlips/lease/{leaseId}/arrears',
  //查询房屋的所有抄表终端数据
  getWaterMeterTerminalList = '/system/v2MeterManage/terminal/house/list/{houseId}',
  //查询开启的房屋的所有抄表终端数据
  getWaterMeterTerminalListOpen = '/system/v2MeterManage/terminal/house/enable/list/{houseId}',
  //添加 抄表终端
  addWaterMeterTerminal = '/system/v2MeterManage/terminal/add',

  //修改 抄表终端
  editWaterMeterTerminal = '/system/v2MeterManage/terminal/update',
  //根据小区id获取可选的抄表终端类型
  getWaterMeterTerminalTypeList = '/system/v2MeterManage/terminal/type/{courtyardId}',
  //根据小区可选的抄表类型收费项
  getWaterMeterTypeFeeList = '/system/v2MeterManage/terminal/type/paymentOption/{courtyardId}',
  //获取生成记录内的缴费明细-分页
  getGenerateRecordDetail = '/nuanqi/nuanqiLog/list/paymentSlipsDetails/{warmId}',

  //欠费明细列表
  getArrearsList = '/gzf/statistics/queryArrearsList',
  //修改终端状态
  updateTerminalStatus = '/system/v2MeterManage/terminal/updateStatus',
  //删除终端
  deleteTerminal = '/terminal/force/delete/{id}',
  //查询合约的所有抄表累计值数据，关联终端表
  getWaterMeterTerminalListWithMoney = '/system/v2MeterManage/total/lease/list/{leaseId}',
  //获取收费项目
  getPaymentItem = '/gzf/statistics/chargingRelationshipList',
  //实收统计列表
  getActualCollectionList = '/gzf/statistics/dataStatistics',
  //获取小区可用优惠
  getDiscountList = '/payment/paymentSlips/youhui/list/{courtyardId}',
  //计算优惠
  calculateDiscount = '/payment/paymentSlips/youhui/calculation',
}
function fillStringTemplate(template: string | undefined, data) {
  // 使用正则表达式匹配模板中的占位符
  const regex = /{([^}]+)}/g;

  // 使用 replace 方法替换占位符
  const filledString = template?.replace(regex, (match, key) => {
    // 从数据对象中查找与占位符匹配的键
    const replacement = data[key.trim()];
    // 如果找到匹配的值，则返回替换后的值，否则返回占位符本身
    return replacement !== undefined ? replacement : match;
  });

  return filledString;
}
//获取水抄记录列表
export const getWaterMeterRecordList = (params) => defHttp.post({ url: Api.getWaterMeterRecordList, params });
//导出模板
export const exportTemplate = (params) => exportExcel(Api.exportTemplate, params);
//导入数据
export const importData = (data, params) => {
  const url = fillStringTemplate(Api.importData, params);
  return defHttp.post(
    {
      url,
      data,
      headers: formHeader,
    },
    {
      isTransformResponse: false,
    },
  );
};
//获取水费配置
export const getWaterConfig = (params) => defHttp.post({ url: Api.getWaterConfig, params });

//查询小区列表
export const getCourtyardList = (params?) => defHttp.post({ url: Api.getCourtyardList, params });
//设置小区权限
export const setCourtyardPermission = (params) => defHttp.post({ url: Api.setCourtyardPermission, params });
//物业缴费第一步
export const propertyPayStep1 = (params) => defHttp.post({ url: Api.propertyPayStep1, params });
//物业缴费第二步
export const propertyPayStep2 = (params) => defHttp.post({ url: Api.propertyPayStep2, params });
//获取暖气记录
export const getHeatingRecordList = (params) => defHttp.post({ url: Api.getHeatingRecordList, params });
//生成暖气费
export const generateHeatingFee = (params) => defHttp.post({ url: Api.generateHeatingFee, params });
//清除暖气费
export const clearHeatingFee = (params) => defHttp.post({ url: Api.clearHeatingFee, params });
//获取空置记录
export const getKongzhiList = (params) => defHttp.post({ url: Api.getKongzhiList, params });

//关闭空置
export const closeKongzhi = (params) => defHttp.post({ url: Api.closeKongzhi, params });
//新增空置
export const addKongzhi = (params) => defHttp.post({ url: Api.addKongzhi, params });
//获取车位列表
export const getCheweiList = (params) => defHttp.post({ url: Api.getCheweiList, params });
//新增车位
export const addChewei = (params) => defHttp.post({ url: Api.addChewei, params });
//关闭车位
export const closeChewei = (params) => defHttp.post({ url: Api.closeChewei, params });
//删除车位
export const deleteChewei = (params) => defHttp.post({ url: Api.deleteChewei, params });
//获取抄表列表
export const getChaobiaoList = (params) => defHttp.post({ url: Api.getChaobiaoList, params });
//修改抄表
export const changeChaobiao = (params) => defHttp.post({ url: Api.changeChaobiao, params });
//清除抄表
export const clearChaobiao = (params) => defHttp.post({ url: Api.clearChaobiao, params });
//新增抄表
export const addChaobiao = (params) => defHttp.post({ url: Api.addChaobiao, params });
//生成车位费
export const createCheweiPayInfo = (params) => defHttp.post({ url: Api.createCheweiPayInfo, params });
//获取停暖列表
export const tingnuanList = (params) => defHttp.post({ url: Api.tingnuanList, params });
//获取停暖关闭列表
export const tingnuanCloseList = (params) => defHttp.post({ url: Api.tingnuanCloseList, params });
//获取最后一次抄表数据
export const getLastChaobiaoData = (params) => defHttp.post({ url: Api.getLastChaobiaoData, params });
//获取合约未完成欠费项信息
export const getUnfinishedArrears = (params) => defHttp.post({ url: Api.getUnfinishedArrears, params });
//查询房屋的所有抄表终端数据
export const getWaterMeterTerminalList = (params) => defHttp.post({ url: Api.getWaterMeterTerminalList, params });
//添加 抄表终端
export const addWaterMeterTerminal = (params) => defHttp.post({ url: Api.addWaterMeterTerminal, params });
//根据小区id获取可选的抄表终端类型
export const getWaterMeterTerminalTypeList = (params) =>
  defHttp.post({ url: Api.getWaterMeterTerminalTypeList, params });
//根据小区可选的抄表类型收费项
export const getWaterMeterTypeFeeList = (params) => defHttp.post({ url: Api.getWaterMeterTypeFeeList, params });
//修改 抄表终端
export const editWaterMeterTerminal = (params) => defHttp.post({ url: Api.editWaterMeterTerminal, params });
//获取生成记录内的缴费明细-分页
export const getGenerateRecordDetail = (params) => defHttp.post({ url: Api.getGenerateRecordDetail, params });
//欠费明细列表
export const getArrearsList = (params) => defHttp.post({ url: Api.getArrearsList, params });
//修改终端状态
export const updateTerminalStatus = (params) => defHttp.post({ url: Api.updateTerminalStatus, params });
//删除终端
export const deleteTerminal = (params) => defHttp.post({ url: Api.deleteTerminal, params });
//查询开启的房屋的所有抄表终端数据
export const getWaterMeterTerminalListOpen = (params) =>
  defHttp.post({ url: Api.getWaterMeterTerminalListOpen, params });
//查询合约的所有抄表累计值数据，关联终端表
export const getWaterMeterTerminalListWithMoney = (params) =>
  defHttp.post({ url: Api.getWaterMeterTerminalListWithMoney, params });
//获取收费项目
export const getPaymentItem = (params?) => defHttp.post({ url: Api.getPaymentItem, params });
//实收统计列表
export const getActualCollectionList = (params) => defHttp.post({ url: Api.getActualCollectionList, params });
//获取小区可用优惠
export const getDiscountList = (params?) => defHttp.post({ url: Api.getDiscountList, params });
//计算优惠
export const calculateDiscount = (params) => defHttp.post({ url: Api.calculateDiscount, params });
//获取暖气树
export const getHeatingTree = (params) => defHttp.post({ url: Api.getHeatingTree, params });
//生成暖气费2
export const generateHeatingFee2 = (params) => defHttp.post({ url: Api.generateHeatingFee2, params });
//移除一条暖气记录
export const removeHeatingRecord = (params) => defHttp.post({ url: Api.removeHeatingRecord, params });
